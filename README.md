# TheBasementCB Discord Bridge

The official Discord bridge plugin for The Basement CB! A lightweight Minecraft plugin that bridges chat between your hybrid server and Discord, supporting both Java and Bedrock players seamlessly.

## Features

- 🔗 **Bidirectional Chat Bridge**: Messages flow seamlessly between Minecraft and Discord
- 🎮 **Hybrid Server Support**: Automatically detects and supports both Java and Bedrock players
- 🤖 **Smart Command Filtering**: Prevents bot commands from cluttering your Minecraft chat
- ⚙️ **Easy Configuration**: Simple YAML config with sensible defaults
- 📊 **Server Events**: Relay player joins, leaves, and deaths to Discord
- 🛡️ **Permission System**: Admin commands with proper permission checks
- 🔄 **Hot Reload**: Reload configuration without restarting the server

## Requirements

- **Minecraft Server**: Spigot, Paper, or any Paper-based server (1.19+)
- **Java**: Java 17 or higher
- **Discord Bot**: A Discord bot with message permissions
- **Hybrid Support** (Optional): Geyser + Floodgate for Bedrock player support

## Quick Setup

### 1. Create Discord Bot

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to "Bot" section and create a bot
4. Copy the bot token (keep it secret!)
5. Enable "Message Content Intent" in bot settings
6. Invite bot to your Discord server with "Send Messages" and "Read Message History" permissions

### 2. Get Channel ID

1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click on your desired channel
3. Click "Copy ID"

### 3. Install Plugin

1. Download the plugin JAR file
2. Place it in your server's `plugins/` folder
3. Start/restart your server
4. Edit `plugins/TheBasementCB/config.yml`:

```yaml
discord:
  bot-token: "YOUR_BOT_TOKEN_HERE"
  discord-channel-id: "YOUR_CHANNEL_ID_HERE"
```

5. Run `/thebasementcb reload` or restart the server

## Configuration

The plugin creates a `config.yml` file with the following options:

```yaml
# Discord Bot Configuration
discord:
  bot-token: "YOUR_BOT_TOKEN_HERE"          # Your Discord bot token
  discord-channel-id: "YOUR_CHANNEL_ID_HERE" # Channel ID for bridging
  activity: "Bridging Minecraft Chat"        # Bot status message

# Chat Bridge Settings
chat:
  show-bot-commands-in-mc: false            # Hide Discord bot commands in MC
  command-prefixes:                         # Command prefixes to filter
    - "/"
    - "!"
    - "?"
    - "."
  minecraft-to-discord-format: "<{player}> {message}"
  discord-to-minecraft-format: "[Discord] {username}: {message}"
  relay-server-messages: true               # Relay joins/leaves/deaths

# Logging Configuration
logging:
  debug: false                              # Enable debug logging
  log-bridged-messages: true                # Log all bridged messages
```

## Commands

| Command | Permission | Description |
|---------|------------|-------------|
| `/thebasementcb reload` | `thebasementcb.admin` | Reload plugin configuration |
| `/thebasementcb status` | `thebasementcb.admin` | Show connection status |
| `/thebasementcb test [message]` | `thebasementcb.admin` | Send test message to Discord |
| `/thebasementcb help` | `thebasementcb.admin` | Show help message |

**Aliases**: `/tbc`, `/basementcb`, `/discordbridge`, `/db`, `/dbridge`

## Permissions

- `thebasementcb.admin` - Access to admin commands (default: op)
- `thebasementcb.use` - Basic plugin usage (default: true)
- `discordbridge.admin` - Legacy permission (still supported)
- `discordbridge.use` - Legacy permission (still supported)

## Hybrid Server Support

The plugin automatically detects hybrid servers by checking for:
- Geyser-Spigot plugin
- Floodgate plugin

When detected, it will:
- Add "(Java)" or "(Bedrock)" indicators to player names
- Handle Bedrock player name formatting properly
- Ensure compatibility with cross-platform features

## Building from Source

1. Clone this repository
2. Make sure you have Java 17+ and Maven installed
3. Run: `mvn clean package`
4. Find the compiled JAR: `target/thebasementcb-1.0.0.jar`

## Troubleshooting

### Bot Not Connecting
- Verify your bot token is correct
- Check that "Message Content Intent" is enabled
- Ensure the bot has permissions in your Discord server

### Messages Not Bridging
- Verify the channel ID is correct
- Check bot permissions in the Discord channel
- Look at server console for error messages
- Use `/thebasementcb status` to check connection

### Hybrid Server Issues
- Ensure Geyser and Floodgate are properly installed
- Check that Bedrock players can join successfully
- Verify plugin load order (DiscordBridge should load after Geyser/Floodgate)

### Debug Mode
Enable debug logging in config.yml:
```yaml
logging:
  debug: true
```

## Support

If you encounter issues:
1. Check the console for error messages
2. Enable debug logging
3. Use `/thebasementcb status` to check connection
4. Verify your Discord bot setup

## License

This project is licensed under the MIT License.
