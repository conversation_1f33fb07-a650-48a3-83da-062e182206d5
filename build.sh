#!/bin/bash

# TheBasementCB Discord Bridge Build Script

echo "🔨 Building TheBasementCB Discord Bridge..."

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed! Please install <PERSON><PERSON> first."
    exit 1
fi

# Check Java version
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$java_version" -lt 17 ]; then
    echo "❌ Java 17 or higher is required! Current version: $java_version"
    exit 1
fi

echo "✅ Java version: $java_version"
echo "✅ <PERSON><PERSON> found"

# Clean and compile
echo "🧹 Cleaning previous builds..."
mvn clean

echo "📦 Compiling and packaging..."
mvn package

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📁 Plugin JAR location: target/thebasementcb-1.0.0.jar"
    echo ""
    echo "🚀 Installation Instructions:"
    echo "1. Copy target/thebasementcb-1.0.0.jar to your server's plugins/ folder"
    echo "2. Start/restart your server"
    echo "3. Edit plugins/TheBasementCB/config.yml with your bot token and channel ID"
    echo "4. Run /thebasementcb reload or restart the server"
    echo ""
    echo "📖 See README.md for detailed setup instructions"
else
    echo "❌ Build failed! Check the error messages above."
    exit 1
fi
