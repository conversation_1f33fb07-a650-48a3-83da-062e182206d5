# 🎮 TheBasementCB Discord Bridge - Plugin Renamed!

## 🔄 What Changed

The plugin has been officially renamed from "DiscordBridge" to **"TheBasementCB"** to match your server's branding!

## 📦 New Plugin Details

- **Plugin Name**: TheBasementCB
- **JAR File**: `target/thebasementcb-1.0.0.jar`
- **Config Folder**: `plugins/TheBasementCB/`
- **Main Command**: `/thebasementcb`

## 🎯 Commands & Aliases

### Primary Commands
- `/thebasementcb reload` - Reload configuration
- `/thebasementcb status` - Show connection status  
- `/thebasementcb test [message]` - Send test message
- `/thebasementcb help` - Show help

### Aliases (All work the same!)
- `/tbc` - Short and sweet
- `/basementcb` - Alternative
- `/discordbridge` - Legacy support
- `/db` - Legacy short
- `/dbridge` - Legacy alternative

## 🔐 Permissions

### New Permissions
- `thebasementcb.admin` - Admin access (default: op)
- `thebasementcb.use` - Basic usage (default: true)

### Legacy Permissions (Still Work!)
- `discordbridge.admin` - Still supported for compatibility
- `discordbridge.use` - Still supported for compatibility

## 📁 File Structure

```
plugins/
└── TheBasementCB/
    ├── config.yml
    └── (plugin data)
```

## ⚙️ Configuration

Your existing configuration will work unchanged! The plugin will:
- Create `plugins/TheBasementCB/config.yml` on first run
- Preserve all your existing settings
- Use the same Discord bot token and channel ID

## 🚀 Installation/Update

### New Installation
```bash
# Copy the renamed plugin
cp target/thebasementcb-1.0.0.jar /path/to/server/plugins/

# Start server - it will create plugins/TheBasementCB/config.yml
# Edit the config with your bot token and channel ID
# Run: /thebasementcb reload
```

### Updating from Old Version
```bash
# Stop server
# Remove old plugin (if exists)
rm plugins/discord-bridge-*.jar

# Copy new plugin
cp target/thebasementcb-1.0.0.jar /path/to/server/plugins/

# Start server
# Your config will be preserved!
```

## 🎨 Branding Updates

### Console Messages
- "TheBasementCB Discord Bridge has been enabled!"
- "TheBasementCB configuration reloaded!"
- "=== TheBasementCB Status ==="

### Discord Bot Activity
- Default status: "The Basement CB Bridge"
- Customizable in config.yml

### Plugin Description
- "TheBasementCB Discord Bridge - Chat bridge for The Basement CB hybrid server"

## 🔧 Features Unchanged

✅ **All existing features work exactly the same:**
- Bidirectional chat bridge
- Loop prevention (no more duplicates!)
- Hybrid server support (Java + Bedrock)
- Command filtering
- Server event relay
- Hot reload capability

## 🧪 Testing Commands

After installation, test with:
```bash
/thebasementcb status
/tbc test Hello from The Basement CB!
/basementcb help
```

## 🔄 Backward Compatibility

**Don't worry about breaking changes!**
- Old commands still work (`/discordbridge`, `/db`, `/dbridge`)
- Old permissions still work (`discordbridge.admin`)
- Configuration format is identical
- All features preserved

## 📋 Quick Reference

| Old | New | Status |
|-----|-----|--------|
| `discord-bridge-1.0.0.jar` | `thebasementcb-1.0.0.jar` | ✅ Renamed |
| `plugins/DiscordBridge/` | `plugins/TheBasementCB/` | ✅ Renamed |
| `/discordbridge` | `/thebasementcb` | ✅ Both work |
| `discordbridge.admin` | `thebasementcb.admin` | ✅ Both work |

## 🎉 Ready to Go!

Your **TheBasementCB Discord Bridge** is ready with:
- ✅ Custom branding for The Basement CB
- ✅ Loop prevention fix applied
- ✅ Hybrid server support
- ✅ Full backward compatibility
- ✅ Multiple command aliases
- ✅ Professional naming

Perfect for The Basement CB community! 🏠🎮
