# 🔄 Loop Prevention Fix - Update Guide

## Issue Fixed
The plugin was creating message loops where:
- Messages sent from Minecraft appeared twice in Discord
- Messages sent from Discord appeared twice in server console
- This created an infinite loop of duplicate messages

## What Was Fixed

### 1. **Bot Message Detection**
- Added proper bot user ID tracking
- Double-check to ignore messages from our own bot
- Prevents the bot from responding to its own messages

### 2. **Message Loop Prevention**
- Added message caching system with 5-second timeout
- Tracks recent messages to prevent immediate duplicates
- Automatic cleanup of old cache entries

### 3. **Channel Verification**
- Improved channel ID comparison for Discord messages
- Only processes messages from the configured bridge channel

### 4. **Modern API Usage**
- Updated to use Adventure API instead of deprecated ChatColor
- Better message handling and formatting

## How to Update

### Option 1: Replace Plugin JAR
1. **Stop your server**
2. **Backup your config** (just in case):
   ```bash
   cp plugins/DiscordBridge/config.yml plugins/DiscordBridge/config.yml.backup
   ```
3. **Replace the plugin**:
   ```bash
   cp target/discord-bridge-1.0.0.jar /path/to/your/server/plugins/
   ```
4. **Start your server**

### Option 2: Hot Reload (if server is running)
1. **Replace the JAR** while server is running
2. **Run the reload command**:
   ```
   /discordbridge reload
   ```

## Testing the Fix

### Before Fix:
```
Discord: LordLuckioo: heyy
Minecraft: [Discord] LordLuckioo: heyy
Minecraft: <LordLuckioo (Java)> heyy
Discord: <LordLuckioo (Java)> heyy  ← DUPLICATE!
```

### After Fix:
```
Discord: LordLuckioo: heyy
Minecraft: [Discord] LordLuckioo: heyy
Minecraft: <LordLuckioo (Java)> heyy
Discord: <LordLuckioo (Java)> heyy  ← NO MORE DUPLICATES!
```

## Debug Mode
If you want to see the loop prevention in action, enable debug mode:

```yaml
logging:
  debug: true
```

You'll see messages like:
```
[DiscordBridge] Ignoring potential loop message: heyy
```

## Verification Commands

After updating, test with these commands:

```bash
# Check plugin status
/discordbridge status

# Send test message
/discordbridge test Loop fix working!

# Check for any errors
/discordbridge help
```

## What to Expect

✅ **Messages flow normally between platforms**  
✅ **No duplicate messages**  
✅ **Bot commands still filtered properly**  
✅ **Hybrid server features still work**  
✅ **Server events (joins/leaves) work normally**  

## Rollback (if needed)

If something goes wrong, you can rollback:
1. Stop the server
2. Replace with your previous plugin version
3. Restore config backup if needed
4. Start the server

## Notes

- **Config preserved**: Your existing configuration will work unchanged
- **No data loss**: This is purely a bug fix, no data is affected  
- **Performance**: The message caching is lightweight and auto-cleans
- **Compatibility**: Still works with all existing features

The loop prevention system automatically expires cached messages after 5 seconds, so it won't interfere with legitimate repeated messages or cause memory issues.

Your Discord bridge should now work perfectly without any message duplication! 🎉
