# TheBasementCB Discord Bridge - Example Configuration
# Copy this to plugins/TheBasementCB/config.yml and customize for your server

# Discord Bot Configuration
discord:
  # Your Discord bot token (KEEP THIS SECRET!)
  # Get this from: https://discord.com/developers/applications
  bot-token: "MTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx"
  
  # The Discord channel ID where messages will be bridged
  # Enable Developer Mode in Discord, right-click channel, "Copy ID"
  discord-channel-id: "1234567890123456789"
  
  # Bot activity status (what the bot shows as "Playing")
  activity: "Bridging The Basement CB"

# Chat Bridge Settings
chat:
  # Whether to show bot commands (starting with / or !) in Minecraft chat
  # Set to false to hide commands like "/help" or "!meow" from appearing in-game
  show-bot-commands-in-mc: false
  
  # Custom command prefixes to filter (in addition to / and !)
  # Add any custom prefixes your Discord bots use
  command-prefixes:
    - "/"
    - "!"
    - "?"
    - "."
    - ">"
    - "-"
  
  # Format for messages sent from Minecraft to Discord
  # Available placeholders: {player}, {message}
  minecraft-to-discord-format: "<{player}> {message}"
  
  # Format for messages sent from Discord to Minecraft
  # Available placeholders: {username}, {message}
  # You can use Minecraft color codes with &
  discord-to-minecraft-format: "&b[Discord] &f{username}&7: &f{message}"
  
  # Whether to relay server messages (joins, leaves, deaths, etc.)
  relay-server-messages: true

# Logging Configuration
logging:
  # Enable debug logging for troubleshooting
  debug: false
  
  # Log all bridged messages to console
  log-bridged-messages: true

# Advanced Settings (usually don't need to change these)
advanced:
  # Maximum message length for Discord messages
  max-discord-message-length: 2000
  
  # Maximum message length for Minecraft messages
  max-minecraft-message-length: 100
  
  # Reconnection attempts if Discord connection fails
  max-reconnection-attempts: 5
  
  # Delay between reconnection attempts (in seconds)
  reconnection-delay: 30
