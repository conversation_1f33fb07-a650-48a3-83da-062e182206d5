# Discord Bridge Plugin - Installation Guide

## 🚀 Quick Start

### Step 1: Download the Plugin
The compiled plugin J<PERSON> is located at: `target/discord-bridge-1.0.0.jar` (10.7 MB)

### Step 2: Discord Bot Setup

1. **Create Discord Application**
   - Go to [Discord Developer Portal](https://discord.com/developers/applications)
   - Click "New Application" and give it a name (e.g., "Minecraft Bridge")

2. **Create Bot**
   - Go to "Bot" section in your application
   - Click "Add Bot"
   - Copy the bot token (keep this secret!)

3. **Enable Required Intents**
   - In Bot settings, enable "Message Content Intent"
   - This is required for the bot to read message content

4. **Invite <PERSON><PERSON> to Server**
   - Go to "OAuth2" > "URL Generator"
   - Select "bot" scope
   - Select permissions: "Send Messages", "Read Message History", "View Channels"
   - Copy the generated URL and open it to invite the bot

### Step 3: Get Channel ID

1. Enable Developer Mode in Discord:
   - User Settings > Advanced > Developer Mode (toggle on)

2. Get Channel ID:
   - Right-click on your desired channel
   - Click "Copy ID"

### Step 4: Install Plugin

1. **Copy Plugin**
   ```bash
   cp target/discord-bridge-1.0.0.jar /path/to/your/server/plugins/
   ```

2. **Start Server**
   - Start your Minecraft server
   - The plugin will create a config file at `plugins/DiscordBridge/config.yml`

3. **Configure Plugin**
   - Stop the server
   - Edit `plugins/DiscordBridge/config.yml`:
   ```yaml
   discord:
     bot-token: "YOUR_BOT_TOKEN_HERE"
     discord-channel-id: "YOUR_CHANNEL_ID_HERE"
   ```

4. **Start Server Again**
   - The plugin should connect to Discord automatically
   - Check console for "Successfully connected to Discord!" message

## 🔧 Configuration

### Basic Configuration
```yaml
discord:
  bot-token: "YOUR_BOT_TOKEN_HERE"
  discord-channel-id: "YOUR_CHANNEL_ID_HERE"
  activity: "Bridging Minecraft Chat"

chat:
  show-bot-commands-in-mc: false
  relay-server-messages: true
```

### Hybrid Server Configuration
If you're running a hybrid server (Java + Bedrock), the plugin will automatically detect Geyser/Floodgate and add platform indicators to player names.

## 🧪 Testing

### Test Commands
```bash
# Check plugin status
/discordbridge status

# Send test message to Discord
/discordbridge test Hello from Minecraft!

# Reload configuration
/discordbridge reload
```

### Expected Behavior

1. **Minecraft to Discord**: Player chat messages appear in Discord channel
2. **Discord to Minecraft**: Discord messages appear in Minecraft chat (excluding bot commands)
3. **Server Events**: Join/leave/death messages appear in Discord (if enabled)
4. **Hybrid Support**: Java and Bedrock players are properly identified

## 🐛 Troubleshooting

### Common Issues

**Bot Not Connecting**
- Verify bot token is correct and not expired
- Check "Message Content Intent" is enabled
- Ensure bot has permissions in Discord server

**Messages Not Bridging**
- Verify channel ID is correct (numeric, no quotes in Discord)
- Check bot can see and send messages in the channel
- Look for error messages in server console

**Hybrid Server Issues**
- Ensure Geyser and Floodgate are installed and working
- Plugin should load after Geyser/Floodgate
- Check plugin load order in server startup logs

### Debug Mode
Enable debug logging in config.yml:
```yaml
logging:
  debug: true
```

### Console Commands
```bash
# Check connection status
/discordbridge status

# View detailed logs
tail -f logs/latest.log | grep DiscordBridge
```

## 📋 Requirements

- **Server**: Spigot/Paper 1.19+ (Java 17+)
- **Discord Bot**: With Message Content Intent enabled
- **Permissions**: Bot needs Send Messages, Read Message History
- **Optional**: Geyser + Floodgate for hybrid server support

## 🔄 Updates

To update the plugin:
1. Stop the server
2. Replace the old JAR with the new one
3. Start the server (config will be preserved)

## 📞 Support

If you encounter issues:
1. Check server console for error messages
2. Enable debug logging
3. Use `/discordbridge status` to check connection
4. Verify Discord bot setup and permissions

## 🎉 Success!

Once configured, you should see:
- ✅ "Successfully connected to Discord!" in console
- ✅ Messages flowing between Minecraft and Discord
- ✅ Server events appearing in Discord
- ✅ Bot commands filtered from Minecraft chat

Your Discord-Minecraft bridge is now ready for The Basement CB! 🎮
