# Discord Bridge Plugin Configuration
# 
# IMPORTANT: Replace the values below with your actual Discord bot token and channel ID
# 
# To get your bot token:
# 1. Go to https://discord.com/developers/applications
# 2. Create a new application or select an existing one
# 3. Go to the "Bot" section
# 4. Copy the token (keep this secret!)
#
# To get your channel ID:
# 1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
# 2. Right-click on the channel you want to bridge
# 3. Click "Copy ID"

# Discord Bot Configuration
discord:
  # Your Discord bot token (KEEP THIS SECRET!)
  bot-token: "YOUR_BOT_TOKEN_HERE"
  
  # The Discord channel ID where messages will be bridged
  discord-channel-id: "YOUR_CHANNEL_ID_HERE"
  
  # Bot activity status (what the bot shows as "Playing")
  activity: "Bridging Minecraft Chat"

# Chat Bridge Settings
chat:
  # Whether to show bot commands (starting with / or !) in Minecraft chat
  # Set to false to hide commands like "/help" or "!meow" from appearing in-game
  show-bot-commands-in-mc: false
  
  # Custom command prefixes to filter (in addition to / and !)
  # Add any custom prefixes your Discord bots use
  command-prefixes:
    - "/"
    - "!"
    - "?"
    - "."
  
  # Format for messages sent from Minecraft to Discord
  # Available placeholders: {player}, {message}
  minecraft-to-discord-format: "<{player}> {message}"
  
  # Format for messages sent from Discord to Minecraft
  # Available placeholders: {username}, {message}
  discord-to-minecraft-format: "[Discord] {username}: {message}"
  
  # Whether to relay server messages (joins, leaves, deaths, etc.)
  relay-server-messages: true

# Logging Configuration
logging:
  # Enable debug logging for troubleshooting
  debug: false
  
  # Log all bridged messages to console
  log-bridged-messages: true
