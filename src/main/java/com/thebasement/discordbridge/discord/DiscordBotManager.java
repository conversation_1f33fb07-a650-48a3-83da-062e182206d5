package com.thebasement.discordbridge.discord;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import com.thebasement.discordbridge.listeners.DiscordMessageListener;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.entities.Activity;
import net.dv8tion.jda.api.entities.channel.concrete.TextChannel;
import net.dv8tion.jda.api.requests.GatewayIntent;
import net.dv8tion.jda.api.utils.ChunkingFilter;
import net.dv8tion.jda.api.utils.MemberCachePolicy;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.concurrent.CompletableFuture;

/**
 * Manages Discord bot connection and operations
 */
public class DiscordBotManager {
    
    private final DiscordBridgePlugin plugin;
    private JDA jda;
    private TextChannel bridgeChannel;
    private DiscordMessageListener messageListener;
    private boolean isConnected = false;
    
    public DiscordBotManager(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Initialize Discord bot connection
     */
    public void initialize() {
        FileConfiguration config = plugin.getConfig();
        String botToken = config.getString("discord.bot-token");
        String channelId = config.getString("discord.discord-channel-id");
        String activity = config.getString("discord.activity", "Bridging Minecraft Chat");
        
        try {
            plugin.getLogger().info("Connecting to Discord...");
            
            // Build JDA instance with minimal required intents
            JDABuilder builder = JDABuilder.createDefault(botToken)
                    .setChunkingFilter(ChunkingFilter.NONE)
                    .setMemberCachePolicy(MemberCachePolicy.NONE)
                    .enableIntents(
                            GatewayIntent.GUILD_MESSAGES,
                            GatewayIntent.MESSAGE_CONTENT
                    )
                    .setActivity(Activity.playing(activity));
            
            // Create message listener
            messageListener = new DiscordMessageListener(plugin);
            builder.addEventListeners(messageListener);
            
            // Build JDA asynchronously
            jda = builder.build();
            
            // Wait for JDA to be ready and get the channel
            CompletableFuture.runAsync(() -> {
                try {
                    jda.awaitReady();
                    
                    // Get the bridge channel
                    bridgeChannel = jda.getTextChannelById(channelId);
                    if (bridgeChannel == null) {
                        plugin.getLogger().severe("Could not find Discord channel with ID: " + channelId);
                        plugin.getLogger().severe("Make sure the bot has access to the channel and the ID is correct!");
                        return;
                    }
                    
                    isConnected = true;
                    plugin.getLogger().info("Successfully connected to Discord!");
                    plugin.getLogger().info("Bridge channel: #" + bridgeChannel.getName() + 
                                          " in " + bridgeChannel.getGuild().getName());
                    
                    // Send connection message if enabled
                    if (config.getBoolean("chat.relay-server-messages", true)) {
                        sendToDiscord("🟢 **Minecraft server is now online!**");
                    }
                    
                } catch (InterruptedException e) {
                    plugin.getLogger().severe("Failed to connect to Discord: " + e.getMessage());
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    plugin.getLogger().severe("Error during Discord connection: " + e.getMessage());
                    if (config.getBoolean("logging.debug", false)) {
                        e.printStackTrace();
                    }
                }
            });
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to initialize Discord bot: " + e.getMessage());
            if (config.getBoolean("logging.debug", false)) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Send a message to the Discord bridge channel
     */
    public void sendToDiscord(String message) {
        if (!isConnected || bridgeChannel == null) {
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                plugin.getLogger().warning("Attempted to send message to Discord but bot is not connected");
            }
            return;
        }
        
        try {
            // Escape Discord markdown characters in the message
            String escapedMessage = escapeDiscordMarkdown(message);
            
            bridgeChannel.sendMessage(escapedMessage).queue(
                success -> {
                    if (plugin.getConfig().getBoolean("logging.log-bridged-messages", true)) {
                        plugin.getLogger().info("Sent to Discord: " + message);
                    }
                },
                error -> {
                    plugin.getLogger().warning("Failed to send message to Discord: " + error.getMessage());
                    if (plugin.getConfig().getBoolean("logging.debug", false)) {
                        error.printStackTrace();
                    }
                }
            );
        } catch (Exception e) {
            plugin.getLogger().warning("Error sending message to Discord: " + e.getMessage());
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Escape Discord markdown characters to prevent formatting issues
     */
    private String escapeDiscordMarkdown(String message) {
        return message
                .replace("\\", "\\\\")
                .replace("*", "\\*")
                .replace("_", "\\_")
                .replace("~", "\\~")
                .replace("`", "\\`")
                .replace("|", "\\|");
    }
    
    /**
     * Shutdown Discord bot connection
     */
    public void shutdown() {
        if (jda != null) {
            try {
                // Send disconnect message if enabled
                if (isConnected && plugin.getConfig().getBoolean("chat.relay-server-messages", true)) {
                    sendToDiscord("🔴 **Minecraft server is going offline!**");
                    
                    // Wait a moment for the message to send
                    Thread.sleep(1000);
                }
                
                plugin.getLogger().info("Disconnecting from Discord...");
                jda.shutdown();
                isConnected = false;
                
            } catch (Exception e) {
                plugin.getLogger().warning("Error during Discord bot shutdown: " + e.getMessage());
            }
        }
    }
    
    /**
     * Get connection status
     */
    public boolean isConnected() {
        return isConnected && jda != null && jda.getStatus() == JDA.Status.CONNECTED;
    }
    
    /**
     * Get Discord bot status information
     */
    public String getStatusInfo() {
        if (jda == null) {
            return "Not initialized";
        }
        
        StringBuilder status = new StringBuilder();
        status.append("Status: ").append(jda.getStatus().name()).append("\n");
        status.append("Connected: ").append(isConnected ? "Yes" : "No").append("\n");
        status.append("Guilds: ").append(jda.getGuilds().size()).append("\n");
        
        if (bridgeChannel != null) {
            status.append("Bridge Channel: #").append(bridgeChannel.getName())
                  .append(" in ").append(bridgeChannel.getGuild().getName());
        } else {
            status.append("Bridge Channel: Not found");
        }
        
        return status.toString();
    }
    
    // Getters
    public JDA getJDA() {
        return jda;
    }
    
    public TextChannel getBridgeChannel() {
        return bridgeChannel;
    }
}
