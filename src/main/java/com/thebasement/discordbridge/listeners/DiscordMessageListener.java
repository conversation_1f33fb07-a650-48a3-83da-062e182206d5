package com.thebasement.discordbridge.listeners;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import com.thebasement.discordbridge.utils.MessageFilter;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.User;
import net.dv8tion.jda.api.entities.channel.concrete.TextChannel;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Listens to Discord messages and relays them to Minecraft
 * Includes command filtering to prevent bot commands from appearing in-game
 */
public class DiscordMessageListener extends ListenerAdapter {

    private final DiscordBridgePlugin plugin;
    private final MessageFilter messageFilter;
    // Track recent messages to prevent loops
    private final ConcurrentHashMap<String, Long> recentMessages = new ConcurrentHashMap<>();
    private static final long MESSAGE_CACHE_DURATION = TimeUnit.SECONDS.toMillis(5);

    public DiscordMessageListener(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
        this.messageFilter = new MessageFilter(plugin);
    }
    
    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        // Ignore messages from bots (including our own bot)
        if (event.getAuthor().isBot()) {
            return;
        }

        // Double-check: ignore messages from our own bot using stored bot ID
        String botUserId = plugin.getDiscordBotManager().getBotUserId();
        if (botUserId != null && event.getAuthor().getId().equals(botUserId)) {
            return;
        }
        
        // Only process messages from the configured bridge channel
        TextChannel bridgeChannel = plugin.getDiscordBotManager().getBridgeChannel();
        if (bridgeChannel == null || !event.getChannel().getId().equals(bridgeChannel.getId())) {
            return;
        }
        
        User author = event.getAuthor();
        Message message = event.getMessage();
        String content = message.getContentRaw();
        
        // Skip empty messages
        if (content.trim().isEmpty()) {
            return;
        }
        
        // Filter out bot commands if configured
        if (messageFilter.shouldFilterMessage(content)) {
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                plugin.getLogger().info("Filtered Discord command: " + content);
            }
            return;
        }

        // Check for message loops - if we recently sent this exact message, ignore it
        String messageKey = author.getId() + ":" + content;
        Long lastSent = recentMessages.get(messageKey);
        long currentTime = System.currentTimeMillis();

        if (lastSent != null && (currentTime - lastSent) < MESSAGE_CACHE_DURATION) {
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                plugin.getLogger().info("Ignoring potential loop message: " + content);
            }
            return;
        }

        // Clean up old entries from cache
        recentMessages.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > MESSAGE_CACHE_DURATION);

        // Add this message to recent messages
        recentMessages.put(messageKey, currentTime);
        
        // Format message for Minecraft
        String minecraftMessage = formatDiscordToMinecraft(author, content);
        
        // Send to Minecraft chat (must be done on main thread)
        Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                // Broadcast to all online players using modern Adventure API
                Component messageComponent = LegacyComponentSerializer.legacyAmpersand().deserialize(minecraftMessage);
                Bukkit.getServer().broadcast(messageComponent);

                // Log if enabled
                if (plugin.getConfig().getBoolean("logging.log-bridged-messages", true)) {
                    String plainText = LegacyComponentSerializer.legacyAmpersand().serialize(messageComponent);
                    plugin.getLogger().info("Bridged to Minecraft: " + plainText);
                }
                
            } catch (Exception e) {
                plugin.getLogger().warning("Error broadcasting Discord message to Minecraft: " + e.getMessage());
                if (plugin.getConfig().getBoolean("logging.debug", false)) {
                    e.printStackTrace();
                }
            }
        });
    }
    
    /**
     * Format Discord message for Minecraft chat
     */
    private String formatDiscordToMinecraft(User author, String content) {
        FileConfiguration config = plugin.getConfig();
        String format = config.getString("chat.discord-to-minecraft-format", "[Discord] {username}: {message}");
        
        // Clean up the username (remove discriminator if present)
        String username = author.getEffectiveName();
        
        // Apply color formatting
        String formattedMessage = format
                .replace("{username}", username)
                .replace("{message}", content);
        
        // Return formatted message (Adventure API will handle color codes)
        return formattedMessage;
    }
}
