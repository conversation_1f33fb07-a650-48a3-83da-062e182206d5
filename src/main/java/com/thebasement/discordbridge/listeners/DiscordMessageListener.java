package com.thebasement.discordbridge.listeners;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import com.thebasement.discordbridge.utils.MessageFilter;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.User;
import net.dv8tion.jda.api.entities.channel.concrete.TextChannel;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * Listens to Discord messages and relays them to Minecraft
 * Includes command filtering to prevent bot commands from appearing in-game
 */
public class DiscordMessageListener extends ListenerAdapter {
    
    private final DiscordBridgePlugin plugin;
    private final MessageFilter messageFilter;
    
    public DiscordMessageListener(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
        this.messageFilter = new MessageFilter(plugin);
    }
    
    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        // Ignore messages from bots (including our own bot)
        if (event.getAuthor().isBot()) {
            return;
        }
        
        // Only process messages from the configured bridge channel
        TextChannel bridgeChannel = plugin.getDiscordBotManager().getBridgeChannel();
        if (bridgeChannel == null || !event.getChannel().equals(bridgeChannel)) {
            return;
        }
        
        User author = event.getAuthor();
        Message message = event.getMessage();
        String content = message.getContentRaw();
        
        // Skip empty messages
        if (content.trim().isEmpty()) {
            return;
        }
        
        // Filter out bot commands if configured
        if (messageFilter.shouldFilterMessage(content)) {
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                plugin.getLogger().info("Filtered Discord command: " + content);
            }
            return;
        }
        
        // Format message for Minecraft
        String minecraftMessage = formatDiscordToMinecraft(author, content);
        
        // Send to Minecraft chat (must be done on main thread)
        Bukkit.getScheduler().runTask(plugin, () -> {
            try {
                // Broadcast to all online players
                Bukkit.broadcastMessage(minecraftMessage);
                
                // Log if enabled
                if (plugin.getConfig().getBoolean("logging.log-bridged-messages", true)) {
                    plugin.getLogger().info("Bridged to Minecraft: " + ChatColor.stripColor(minecraftMessage));
                }
                
            } catch (Exception e) {
                plugin.getLogger().warning("Error broadcasting Discord message to Minecraft: " + e.getMessage());
                if (plugin.getConfig().getBoolean("logging.debug", false)) {
                    e.printStackTrace();
                }
            }
        });
    }
    
    /**
     * Format Discord message for Minecraft chat
     */
    private String formatDiscordToMinecraft(User author, String content) {
        FileConfiguration config = plugin.getConfig();
        String format = config.getString("chat.discord-to-minecraft-format", "[Discord] {username}: {message}");
        
        // Clean up the username (remove discriminator if present)
        String username = author.getEffectiveName();
        
        // Apply color formatting
        String formattedMessage = format
                .replace("{username}", username)
                .replace("{message}", content);
        
        // Convert to Minecraft color codes
        return ChatColor.translateAlternateColorCodes('&', formattedMessage);
    }
}
