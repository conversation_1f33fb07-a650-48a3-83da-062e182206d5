package com.thebasement.discordbridge.listeners;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import com.thebasement.discordbridge.discord.DiscordBotManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.configuration.file.FileConfiguration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Listens to Minecraft events and relays them to Discord
 * Supports both Java and Bedrock players on hybrid servers
 */
public class MinecraftChatListener implements Listener {

    private final DiscordBridgePlugin plugin;
    // Track recent messages to prevent loops
    private final ConcurrentHashMap<String, Long> recentMessages = new ConcurrentHashMap<>();
    private static final long MESSAGE_CACHE_DURATION = TimeUnit.SECONDS.toMillis(5);

    public MinecraftChatListener(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Handle player chat messages
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        String message = event.getMessage();
        
        // Get Discord bot manager
        DiscordBotManager discordManager = plugin.getDiscordBotManager();
        if (discordManager == null || !discordManager.isConnected()) {
            return;
        }

        // Check for message loops - prevent sending messages that came from Discord
        String messageKey = player.getName() + ":" + message;
        Long lastSent = recentMessages.get(messageKey);
        long currentTime = System.currentTimeMillis();

        if (lastSent != null && (currentTime - lastSent) < MESSAGE_CACHE_DURATION) {
            if (plugin.getConfig().getBoolean("logging.debug", false)) {
                plugin.getLogger().info("Ignoring potential loop message from " + player.getName() + ": " + message);
            }
            return;
        }

        // Clean up old entries from cache
        recentMessages.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > MESSAGE_CACHE_DURATION);

        // Add this message to recent messages
        recentMessages.put(messageKey, currentTime);

        // Format the message for Discord
        String formattedMessage = formatMinecraftToDiscord(player, message);

        // Send to Discord
        discordManager.sendToDiscord(formattedMessage);
        
        // Log if enabled
        if (plugin.getConfig().getBoolean("logging.log-bridged-messages", true)) {
            plugin.getLogger().info("Bridged to Discord: " + formattedMessage);
        }
    }
    
    /**
     * Handle player join events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        if (!plugin.getConfig().getBoolean("chat.relay-server-messages", true)) {
            return;
        }
        
        Player player = event.getPlayer();
        DiscordBotManager discordManager = plugin.getDiscordBotManager();
        
        if (discordManager != null && discordManager.isConnected()) {
            String playerName = plugin.getFormattedPlayerName(player.getName());
            String joinMessage = "➡️ **" + playerName + " joined the server**";
            discordManager.sendToDiscord(joinMessage);
        }
    }
    
    /**
     * Handle player quit events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        if (!plugin.getConfig().getBoolean("chat.relay-server-messages", true)) {
            return;
        }
        
        Player player = event.getPlayer();
        DiscordBotManager discordManager = plugin.getDiscordBotManager();
        
        if (discordManager != null && discordManager.isConnected()) {
            String playerName = plugin.getFormattedPlayerName(player.getName());
            String quitMessage = "⬅️ **" + playerName + " left the server**";
            discordManager.sendToDiscord(quitMessage);
        }
    }
    
    /**
     * Handle player death events
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        if (!plugin.getConfig().getBoolean("chat.relay-server-messages", true)) {
            return;
        }
        
        Player player = event.getEntity();
        String deathMessage = event.getDeathMessage();
        
        if (deathMessage == null || deathMessage.trim().isEmpty()) {
            return;
        }
        
        DiscordBotManager discordManager = plugin.getDiscordBotManager();
        if (discordManager != null && discordManager.isConnected()) {
            // Format death message for hybrid server support
            String formattedDeathMessage = formatDeathMessage(player, deathMessage);
            String discordMessage = "💀 **" + formattedDeathMessage + "**";
            discordManager.sendToDiscord(discordMessage);
        }
    }
    
    /**
     * Format Minecraft chat message for Discord
     */
    private String formatMinecraftToDiscord(Player player, String message) {
        FileConfiguration config = plugin.getConfig();
        String format = config.getString("chat.minecraft-to-discord-format", "<{player}> {message}");
        
        // Get formatted player name (with Java/Bedrock indicator if hybrid server)
        String playerName = plugin.getFormattedPlayerName(player.getName());
        
        return format
                .replace("{player}", playerName)
                .replace("{message}", message);
    }
    
    /**
     * Format death message for hybrid server support
     */
    private String formatDeathMessage(Player player, String deathMessage) {
        if (!plugin.isHybridServer()) {
            return deathMessage;
        }
        
        // Replace the player name in the death message with the formatted version
        String originalName = player.getName();
        String formattedName = plugin.getFormattedPlayerName(originalName);
        
        return deathMessage.replace(originalName, formattedName);
    }
}
