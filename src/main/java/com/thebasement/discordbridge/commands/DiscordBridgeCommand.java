package com.thebasement.discordbridge.commands;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import com.thebasement.discordbridge.discord.DiscordBotManager;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command handler for Discord Bridge plugin
 */
public class DiscordBridgeCommand implements CommandExecutor, TabCompleter {
    
    private final DiscordBridgePlugin plugin;
    
    public DiscordBridgeCommand(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("discordbridge.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                handleReload(sender);
                break;
                
            case "status":
                handleStatus(sender);
                break;
                
            case "test":
                handleTest(sender, args);
                break;
                
            case "help":
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    /**
     * Handle reload command
     */
    private void handleReload(CommandSender sender) {
        sender.sendMessage(ChatColor.YELLOW + "Reloading Discord Bridge configuration...");
        
        try {
            plugin.reloadPlugin();
            sender.sendMessage(ChatColor.GREEN + "Discord Bridge configuration reloaded successfully!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Failed to reload configuration: " + e.getMessage());
            plugin.getLogger().severe("Error during reload: " + e.getMessage());
        }
    }
    
    /**
     * Handle status command
     */
    private void handleStatus(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Discord Bridge Status ===");
        
        DiscordBotManager botManager = plugin.getDiscordBotManager();
        if (botManager == null) {
            sender.sendMessage(ChatColor.RED + "Discord bot manager is not initialized!");
            return;
        }
        
        // Connection status
        boolean connected = botManager.isConnected();
        sender.sendMessage(ChatColor.GRAY + "Connection Status: " + 
                          (connected ? ChatColor.GREEN + "Connected" : ChatColor.RED + "Disconnected"));
        
        // Hybrid server status
        if (plugin.isHybridServer()) {
            sender.sendMessage(ChatColor.GRAY + "Server Type: " + ChatColor.BLUE + "Hybrid (Java + Bedrock)");
        } else {
            sender.sendMessage(ChatColor.GRAY + "Server Type: " + ChatColor.BLUE + "Java Edition");
        }
        
        // Configuration info
        String channelId = plugin.getConfig().getString("discord.discord-channel-id");
        boolean showCommands = plugin.getConfig().getBoolean("chat.show-bot-commands-in-mc");
        boolean relayServerMessages = plugin.getConfig().getBoolean("chat.relay-server-messages");
        
        sender.sendMessage(ChatColor.GRAY + "Bridge Channel ID: " + ChatColor.WHITE + channelId);
        sender.sendMessage(ChatColor.GRAY + "Show Bot Commands: " + 
                          (showCommands ? ChatColor.GREEN + "Yes" : ChatColor.RED + "No"));
        sender.sendMessage(ChatColor.GRAY + "Relay Server Messages: " + 
                          (relayServerMessages ? ChatColor.GREEN + "Yes" : ChatColor.RED + "No"));
        
        // Detailed status if connected
        if (connected) {
            String statusInfo = botManager.getStatusInfo();
            sender.sendMessage(ChatColor.GRAY + "Bot Details:");
            for (String line : statusInfo.split("\n")) {
                sender.sendMessage(ChatColor.GRAY + "  " + line);
            }
        }
    }
    
    /**
     * Handle test command
     */
    private void handleTest(CommandSender sender, String[] args) {
        DiscordBotManager botManager = plugin.getDiscordBotManager();
        if (botManager == null || !botManager.isConnected()) {
            sender.sendMessage(ChatColor.RED + "Discord bot is not connected! Cannot send test message.");
            return;
        }
        
        String testMessage;
        if (args.length > 1) {
            // Join remaining args as test message
            testMessage = String.join(" ", Arrays.copyOfRange(args, 1, args.length));
        } else {
            testMessage = "🧪 Test message from " + sender.getName() + " via Discord Bridge!";
        }
        
        try {
            botManager.sendToDiscord(testMessage);
            sender.sendMessage(ChatColor.GREEN + "Test message sent to Discord!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Failed to send test message: " + e.getMessage());
        }
    }
    
    /**
     * Send help message
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Discord Bridge Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/discordbridge reload" + ChatColor.GRAY + " - Reload plugin configuration");
        sender.sendMessage(ChatColor.YELLOW + "/discordbridge status" + ChatColor.GRAY + " - Show connection status");
        sender.sendMessage(ChatColor.YELLOW + "/discordbridge test [message]" + ChatColor.GRAY + " - Send test message to Discord");
        sender.sendMessage(ChatColor.YELLOW + "/discordbridge help" + ChatColor.GRAY + " - Show this help message");
        
        if (plugin.isHybridServer()) {
            sender.sendMessage(ChatColor.BLUE + "Note: Hybrid server detected - supporting Java & Bedrock players!");
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("discordbridge.admin")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            String partial = args[0].toLowerCase();
            
            for (String subCommand : Arrays.asList("reload", "status", "test", "help")) {
                if (subCommand.startsWith(partial)) {
                    completions.add(subCommand);
                }
            }
            
            return completions;
        }
        
        return new ArrayList<>();
    }
}
