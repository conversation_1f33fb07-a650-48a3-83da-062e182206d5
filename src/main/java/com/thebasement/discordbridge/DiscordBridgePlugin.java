package com.thebasement.discordbridge;

import com.thebasement.discordbridge.discord.DiscordBotManager;
import com.thebasement.discordbridge.listeners.MinecraftChatListener;
import com.thebasement.discordbridge.commands.DiscordBridgeCommand;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * Main plugin class for Discord Bridge
 * Supports both Java and Bedrock players on hybrid servers
 */
public class DiscordBridgePlugin extends JavaPlugin {
    
    private static DiscordBridgePlugin instance;
    private DiscordBotManager discordBotManager;
    private MinecraftChatListener chatListener;
    private boolean isHybridServer = false;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Save default config if it doesn't exist
        saveDefaultConfig();
        
        // Check if this is a hybrid server (Geyser/Floodgate detection)
        detectHybridServer();
        
        // Load configuration
        if (!loadConfiguration()) {
            getLogger().severe("Failed to load configuration! Disabling plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Initialize Discord bot
        initializeDiscordBot();
        
        // Register event listeners
        registerListeners();
        
        // Register commands
        registerCommands();
        
        getLogger().info("Discord Bridge has been enabled!");
        if (isHybridServer) {
            getLogger().info("Hybrid server detected - supporting both Java and Bedrock players!");
        }
    }
    
    @Override
    public void onDisable() {
        // Shutdown Discord bot gracefully
        if (discordBotManager != null) {
            discordBotManager.shutdown();
        }
        
        getLogger().info("Discord Bridge has been disabled!");
    }
    
    /**
     * Detect if this is a hybrid server by checking for Geyser/Floodgate plugins
     */
    private void detectHybridServer() {
        if (getServer().getPluginManager().getPlugin("Geyser-Spigot") != null ||
            getServer().getPluginManager().getPlugin("floodgate") != null ||
            getServer().getPluginManager().getPlugin("Floodgate") != null) {
            isHybridServer = true;
            getLogger().info("Hybrid server plugins detected (Geyser/Floodgate)");
        }
    }
    
    /**
     * Load and validate configuration
     */
    private boolean loadConfiguration() {
        FileConfiguration config = getConfig();
        
        // Check required configuration values
        String botToken = config.getString("discord.bot-token");
        String channelId = config.getString("discord.discord-channel-id");
        
        if (botToken == null || botToken.equals("YOUR_BOT_TOKEN_HERE") || botToken.trim().isEmpty()) {
            getLogger().severe("Discord bot token is not configured! Please set 'discord.bot-token' in config.yml");
            return false;
        }
        
        if (channelId == null || channelId.equals("YOUR_CHANNEL_ID_HERE") || channelId.trim().isEmpty()) {
            getLogger().severe("Discord channel ID is not configured! Please set 'discord.discord-channel-id' in config.yml");
            return false;
        }
        
        // Validate channel ID format (should be numeric)
        try {
            Long.parseLong(channelId);
        } catch (NumberFormatException e) {
            getLogger().severe("Invalid Discord channel ID format! Channel ID should be numeric.");
            return false;
        }
        
        return true;
    }
    
    /**
     * Initialize Discord bot connection
     */
    private void initializeDiscordBot() {
        try {
            discordBotManager = new DiscordBotManager(this);
            discordBotManager.initialize();
        } catch (Exception e) {
            getLogger().severe("Failed to initialize Discord bot: " + e.getMessage());
            if (getConfig().getBoolean("logging.debug", false)) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Register event listeners
     */
    private void registerListeners() {
        chatListener = new MinecraftChatListener(this);
        getServer().getPluginManager().registerEvents(chatListener, this);
    }
    
    /**
     * Register plugin commands
     */
    private void registerCommands() {
        getCommand("discordbridge").setExecutor(new DiscordBridgeCommand(this));
    }
    
    /**
     * Reload plugin configuration and restart Discord bot
     */
    public void reloadPlugin() {
        // Shutdown existing bot
        if (discordBotManager != null) {
            discordBotManager.shutdown();
        }
        
        // Reload config
        reloadConfig();
        
        // Validate new config
        if (!loadConfiguration()) {
            getLogger().severe("Failed to reload configuration!");
            return;
        }
        
        // Restart Discord bot
        initializeDiscordBot();
        
        getLogger().info("Discord Bridge configuration reloaded!");
    }
    
    // Getters
    public static DiscordBridgePlugin getInstance() {
        return instance;
    }
    
    public DiscordBotManager getDiscordBotManager() {
        return discordBotManager;
    }
    
    public boolean isHybridServer() {
        return isHybridServer;
    }
    
    /**
     * Get formatted player name for hybrid server support
     * Bedrock players often have different name formats
     */
    public String getFormattedPlayerName(String playerName) {
        if (!isHybridServer) {
            return playerName;
        }
        
        // Bedrock players typically have a prefix (like a dot) when using Floodgate
        // This can be customized based on your server's Floodgate configuration
        if (playerName.startsWith(".")) {
            return playerName + " (Bedrock)";
        }
        
        return playerName + " (Java)";
    }
}
