package com.thebasement.discordbridge.utils;

import com.thebasement.discordbridge.DiscordBridgePlugin;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Utility class for filtering Discord messages
 * Prevents bot commands from appearing in Minecraft chat
 */
public class MessageFilter {
    
    private final DiscordBridgePlugin plugin;
    private final Pattern urlPattern;
    
    public MessageFilter(DiscordBridgePlugin plugin) {
        this.plugin = plugin;
        
        // Pattern to detect URLs (optional feature)
        this.urlPattern = Pattern.compile(
            "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+",
            Pattern.CASE_INSENSITIVE
        );
    }
    
    /**
     * Check if a message should be filtered out
     */
    public boolean shouldFilterMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return true;
        }
        
        FileConfiguration config = plugin.getConfig();
        
        // If bot commands should be shown in MC, don't filter anything
        if (config.getBoolean("chat.show-bot-commands-in-mc", false)) {
            return false;
        }
        
        String trimmedMessage = message.trim();
        
        // Check against configured command prefixes
        List<String> commandPrefixes = config.getStringList("chat.command-prefixes");
        if (commandPrefixes != null && !commandPrefixes.isEmpty()) {
            for (String prefix : commandPrefixes) {
                if (trimmedMessage.startsWith(prefix)) {
                    return true;
                }
            }
        } else {
            // Default prefixes if none configured
            if (trimmedMessage.startsWith("/") || 
                trimmedMessage.startsWith("!") || 
                trimmedMessage.startsWith("?") || 
                trimmedMessage.startsWith(".")) {
                return true;
            }
        }
        
        // Filter common bot command patterns
        if (isCommonBotCommand(trimmedMessage)) {
            return true;
        }
        
        // Filter messages that are only mentions (like @everyone)
        if (isOnlyMentions(trimmedMessage)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if message matches common bot command patterns
     */
    private boolean isCommonBotCommand(String message) {
        String lowerMessage = message.toLowerCase();
        
        // Common bot command patterns
        String[] commonPatterns = {
            "help",
            "commands",
            "ping",
            "info",
            "stats",
            "status",
            "version",
            "uptime",
            "serverinfo",
            "userinfo",
            "avatar",
            "invite",
            "support"
        };
        
        // Check if message starts with common command words after prefix
        for (String pattern : commonPatterns) {
            if (lowerMessage.matches("^[/!?.].?" + pattern + ".*")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if message contains only Discord mentions
     */
    private boolean isOnlyMentions(String message) {
        // Remove all Discord mentions and see if anything is left
        String withoutMentions = message
                .replaceAll("<@!?\\d+>", "") // User mentions
                .replaceAll("<@&\\d+>", "")  // Role mentions
                .replaceAll("<#\\d+>", "")   // Channel mentions
                .replaceAll("@everyone", "") // Everyone mention
                .replaceAll("@here", "")     // Here mention
                .trim();
        
        return withoutMentions.isEmpty();
    }
    
    /**
     * Clean message content for Minecraft display
     */
    public String cleanMessageForMinecraft(String message) {
        if (message == null) {
            return "";
        }
        
        // Remove or replace Discord-specific formatting
        String cleaned = message
                // Remove Discord markdown
                .replaceAll("\\*\\*(.*?)\\*\\*", "$1") // Bold
                .replaceAll("\\*(.*?)\\*", "$1")       // Italic
                .replaceAll("__(.*?)__", "$1")         // Underline
                .replaceAll("~~(.*?)~~", "$1")         // Strikethrough
                .replaceAll("`(.*?)`", "$1")           // Inline code
                .replaceAll("```[\\s\\S]*?```", "[Code Block]") // Code blocks
                
                // Replace Discord mentions with readable text
                .replaceAll("<@!?(\\d+)>", "@User")
                .replaceAll("<@&(\\d+)>", "@Role")
                .replaceAll("<#(\\d+)>", "#channel")
                .replaceAll("@everyone", "@everyone")
                .replaceAll("@here", "@here")
                
                // Shorten URLs if they're too long
                .replaceAll(urlPattern.pattern(), "[Link]");
        
        // Limit message length for Minecraft chat
        if (cleaned.length() > 100) {
            cleaned = cleaned.substring(0, 97) + "...";
        }
        
        return cleaned;
    }
    
    /**
     * Check if message contains inappropriate content
     * This is a basic implementation - you can extend it based on your server's needs
     */
    public boolean containsInappropriateContent(String message) {
        // This is where you could add profanity filtering, spam detection, etc.
        // For now, just check for excessive caps
        if (message.length() > 10) {
            long capsCount = message.chars().filter(Character::isUpperCase).count();
            double capsRatio = (double) capsCount / message.length();
            
            // If more than 70% caps, consider it spam
            return capsRatio > 0.7;
        }
        
        return false;
    }
}
